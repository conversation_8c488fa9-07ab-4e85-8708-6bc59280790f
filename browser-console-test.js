// Copy and paste this into browser console (F12 -> Console tab)
// Make sure you're on localhost:5173 or localhost:5000

async function testAPI() {
  const BASE_URL = 'http://localhost:5000/api/v1';
  
  console.log('🔍 Testing ERP API from Browser Console...');
  
  try {
    // 1. Login
    console.log('1. 🔐 Testing Login...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123456'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login Response:', loginData);
    
    if (!loginData.success) {
      console.error('❌ Login failed:', loginData.message);
      return;
    }
    
    const token = loginData.data.accessToken;
    console.log('✅ Login successful!');
    console.log('🔑 Token:', token.substring(0, 50) + '...');
    
    // 2. Get All Users
    console.log('\n2. 👥 Testing Get All Users...');
    const usersResponse = await fetch(`${BASE_URL}/auth/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const usersData = await usersResponse.json();
    console.log('Users Response:', usersData);
    
    // 3. Get Profile
    console.log('\n3. 👤 Testing Get Profile...');
    const profileResponse = await fetch(`${BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const profileData = await profileResponse.json();
    console.log('Profile Response:', profileData);
    
    // 4. Search Users
    console.log('\n4. 🔍 Testing Search Users...');
    const searchResponse = await fetch(`${BASE_URL}/auth/users/search/admin`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const searchData = await searchResponse.json();
    console.log('Search Response:', searchData);
    
    // 5. Test without token (should fail)
    console.log('\n5. ❌ Testing without token (should fail)...');
    const noTokenResponse = await fetch(`${BASE_URL}/auth/users`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const noTokenData = await noTokenResponse.json();
    console.log('No Token Response:', noTokenData);
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the test
testAPI();
