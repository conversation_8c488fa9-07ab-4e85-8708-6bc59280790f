import { Settings, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, BarChart3 } from 'lucide-react';

const QualityProcess = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Phòng chất lượng quy trình</h1>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quy trình đang chạy</p>
              <p className="text-2xl font-bold text-gray-900">24</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Settings className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quy trình hoàn thành</p>
              <p className="text-2xl font-bold text-gray-900">187</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Cảnh báo chất lượng</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Hiệu suất TB</p>
              <p className="text-2xl font-bold text-gray-900">94.2%</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quản lý quy trình chất lượng</h2>
        <p className="text-gray-600 mb-6">Theo dõi và kiểm soát các quy trình chất lượng trong sản xuất và vận hành.</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Kiểm tra quy trình</h3>
            <p className="text-sm text-gray-600 mb-4">Kiểm tra và đánh giá quy trình hiện tại</p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
              Bắt đầu kiểm tra
            </button>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Cải tiến quy trình</h3>
            <p className="text-sm text-gray-600 mb-4">Đề xuất cải tiến và tối ưu hóa</p>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
              Tạo đề xuất
            </button>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Báo cáo quy trình</h3>
            <p className="text-sm text-gray-600 mb-4">Báo cáo hiệu suất quy trình</p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm">
              Xem báo cáo
            </button>
          </div>
        </div>
      </div>

      {/* Process Quality Metrics */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Chỉ số chất lượng quy trình</h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Tuân thủ tiêu chuẩn</h3>
              <p className="text-sm text-gray-600">Mức độ tuân thủ các tiêu chuẩn chất lượng</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-green-600">96.8%</p>
              <p className="text-sm text-gray-500">+2.1% so với tháng trước</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Thời gian xử lý</h3>
              <p className="text-sm text-gray-600">Thời gian trung bình hoàn thành quy trình</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-600">2.3h</p>
              <p className="text-sm text-gray-500">-0.2h so với tháng trước</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Tỷ lệ lỗi</h3>
              <p className="text-sm text-gray-600">Tỷ lệ lỗi trong quy trình</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-red-600">1.2%</p>
              <p className="text-sm text-gray-500">-0.3% so với tháng trước</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Cải tiến thực hiện</h3>
              <p className="text-sm text-gray-600">Số lượng cải tiến đã triển khai</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-purple-600">12</p>
              <p className="text-sm text-gray-500">+4 so với tháng trước</p>
            </div>
          </div>
        </div>
      </div>

      {/* Process Status */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Trạng thái quy trình hiện tại</h2>

        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quy trình</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trạng thái</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tiến độ</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Chất lượng</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-4 text-sm font-medium text-gray-900">Kiểm tra nguyên liệu</td>
                <td className="px-4 py-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Hoàn thành
                  </span>
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">100%</td>
                <td className="px-4 py-4 text-sm text-green-600 font-medium">Đạt chuẩn</td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-medium text-gray-900">Kiểm tra sản phẩm</td>
                <td className="px-4 py-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    Đang thực hiện
                  </span>
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">75%</td>
                <td className="px-4 py-4 text-sm text-blue-600 font-medium">Đang kiểm tra</td>
              </tr>
              <tr>
                <td className="px-4 py-4 text-sm font-medium text-gray-900">Đóng gói</td>
                <td className="px-4 py-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Chờ xử lý
                  </span>
                </td>
                <td className="px-4 py-4 text-sm text-gray-900">0%</td>
                <td className="px-4 py-4 text-sm text-gray-500">Chưa bắt đầu</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default QualityProcess;
