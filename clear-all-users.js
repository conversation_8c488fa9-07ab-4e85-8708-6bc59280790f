// Script to delete all users from database
const mongoose = require('./backend/node_modules/mongoose');

async function clearAllUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/mydatabase');
    console.log('Connected to MongoDB');
    
    // Define User schema (simplified)
    const userSchema = new mongoose.Schema({
      username: String,
      email: String,
      role: String
    });
    
    const User = mongoose.model('User', userSchema);
    
    // Count users before deletion
    const userCount = await User.countDocuments();
    console.log(`📊 Found ${userCount} users in database`);
    
    if (userCount === 0) {
      console.log('✅ No users to delete');
      return;
    }
    
    // Show all users before deletion
    const users = await User.find({}, 'username email role');
    console.log('\n👥 Users to be deleted:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.username} (${user.email}) - ${user.role}`);
    });
    
    // Delete all users
    console.log('\n🗑️  Deleting all users...');
    const result = await User.deleteMany({});
    
    console.log(`✅ Successfully deleted ${result.deletedCount} users`);
    
    // Verify deletion
    const remainingCount = await User.countDocuments();
    console.log(`📊 Remaining users: ${remainingCount}`);
    
    if (remainingCount === 0) {
      console.log('🎉 All users have been successfully deleted!');
    } else {
      console.log('⚠️  Some users may still remain');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Confirmation prompt
console.log('⚠️  WARNING: This will delete ALL users from the database!');
console.log('⚠️  This action cannot be undone!');
console.log('');
console.log('If you are sure you want to proceed, the script will run in 3 seconds...');
console.log('Press Ctrl+C to cancel');

setTimeout(() => {
  clearAllUsers();
}, 3000);
