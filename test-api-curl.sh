#!/bin/bash
# API Testing Script using curl

BASE_URL="http://localhost:5000/api/v1"

echo "🔍 Testing ERP API Endpoints..."
echo "================================"

# 1. Test Login
echo "1. Testing Login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:5173" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123456"
  }')

echo "Login Response: $LOGIN_RESPONSE"

# Extract token (requires jq - install with: npm install -g jq)
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.accessToken')

if [ "$TOKEN" != "null" ] && [ "$TOKEN" != "" ]; then
  echo "✅ Login successful"
  echo "🔑 Token: ${TOKEN:0:50}..."
  echo ""
  
  # 2. Test Get All Users
  echo "2. Testing Get All Users..."
  USERS_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/users" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Users Response: $USERS_RESPONSE"
  echo ""
  
  # 3. Test Get Profile
  echo "3. Testing Get Profile..."
  PROFILE_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/profile" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Profile Response: $PROFILE_RESPONSE"
  echo ""
  
  # 4. Test Search Users
  echo "4. Testing Search Users..."
  SEARCH_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/users/search/admin" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Search Response: $SEARCH_RESPONSE"
  echo ""
  
  # 5. Test without token (should fail)
  echo "5. Testing without token (should fail)..."
  NO_TOKEN_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/users" \
    -H "Content-Type: application/json")
  
  echo "No Token Response: $NO_TOKEN_RESPONSE"
  
else
  echo "❌ Login failed"
fi

echo ""
echo "🎉 API Testing completed!"
