// Test endpoint with proper authentication for browser
const API_BASE = 'http://localhost:5000/api/v1';

async function testWithAuth() {
  try {
    // 1. <PERSON>gin to get token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123456'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.log('❌ Login failed:', loginData.message);
      return;
    }

    const token = loginData.data.accessToken;
    console.log('✅ Login successful');
    console.log('🔑 Token:', token.substring(0, 50) + '...\n');

    // 2. Test the users endpoint
    console.log('👥 Testing /auth/users endpoint...');
    const usersResponse = await fetch(`${API_BASE}/auth/users`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const usersData = await usersResponse.json();
    
    if (usersData.success) {
      console.log('✅ Users endpoint works!');
      console.log('📊 Total users:', usersData.data.data.length);
      console.log('👤 Users:');
      usersData.data.data.forEach(user => {
        console.log(`  - ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
      });
    } else {
      console.log('❌ Users endpoint failed:', usersData.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testWithAuth();
