import React from 'react';
import { Users, Award, TrendingUp, CheckCircle } from 'lucide-react';

const QualityPersonnel = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Phòng chất lượng nhân sự</h1>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Nhân viên đánh giá</p>
              <p className="text-2xl font-bold text-gray-900">142</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Điểm TB chất lượng</p>
              <p className="text-2xl font-bold text-gray-900">8.7</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <Award className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Cải thiện (%)</p>
              <p className="text-2xl font-bold text-gray-900">+12%</p>
            </div>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Hoàn thành KPI</p>
              <p className="text-2xl font-bold text-gray-900">89%</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quản lý chất lượng nhân sự</h2>
        <p className="text-gray-600 mb-6">Theo dõi và đánh giá chất lượng công việc của nhân viên trong bộ phận chất lượng.</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Đánh giá hiệu suất</h3>
            <p className="text-sm text-gray-600 mb-4">Đánh giá định kỳ hiệu suất làm việc</p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
              Tạo đánh giá
            </button>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Kế hoạch đào tạo</h3>
            <p className="text-sm text-gray-600 mb-4">Lập kế hoạch đào tạo nâng cao</p>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
              Xem kế hoạch
            </button>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Báo cáo chất lượng</h3>
            <p className="text-sm text-gray-600 mb-4">Báo cáo tổng hợp chất lượng nhân sự</p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm">
              Xem báo cáo
            </button>
          </div>
        </div>
      </div>

      {/* Quality Metrics */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Chỉ số chất lượng nhân sự</h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Chất lượng công việc</h3>
              <p className="text-sm text-gray-600">Đánh giá chất lượng output công việc</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-green-600">8.5/10</p>
              <p className="text-sm text-gray-500">+0.3 so với tháng trước</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Tuân thủ quy trình</h3>
              <p className="text-sm text-gray-600">Mức độ tuân thủ quy trình làm việc</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-600">92%</p>
              <p className="text-sm text-gray-500">+5% so với tháng trước</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Kỹ năng chuyên môn</h3>
              <p className="text-sm text-gray-600">Đánh giá kỹ năng chuyên môn</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-purple-600">8.2/10</p>
              <p className="text-sm text-gray-500">+0.1 so với tháng trước</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualityPersonnel;
