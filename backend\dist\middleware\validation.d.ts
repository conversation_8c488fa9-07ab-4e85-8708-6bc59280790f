import { Request, Response, NextFunction } from 'express';
import { Valida<PERSON><PERSON>hain } from 'express-validator';
export declare const handleValidationErrors: (req: Request, res: Response, next: NextFunction) => void;
export declare const validate: (validations: ValidationChain[]) => (((req: Request, res: Response, next: NextFunction) => void) | ValidationChain)[];
//# sourceMappingURL=validation.d.ts.map