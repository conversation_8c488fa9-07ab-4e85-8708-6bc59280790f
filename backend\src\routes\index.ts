import { Router } from 'express';
import authRoutes from './authRoutes';

const router = Router();

// Health check route
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test CORS endpoint
router.post('/test-cors', (req, res) => {
  console.log('🔍 Test CORS Request:', {
    origin: req.headers.origin,
    method: req.method,
    body: req.body,
    headers: req.headers
  });

  res.json({
    success: true,
    message: 'CORS test successful',
    data: {
      origin: req.headers.origin,
      method: req.method,
      body: req.body
    },
    timestamp: new Date().toISOString()
  });
});

// API routes
router.use('/auth', authRoutes);

export default router;
