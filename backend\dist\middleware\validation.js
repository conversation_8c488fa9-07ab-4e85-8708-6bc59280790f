"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validate = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const utils_1 = require("@/utils");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const formattedErrors = errors.array().map(error => ({
            field: error.type === 'field' ? error.path : 'unknown',
            message: error.msg,
            value: error.type === 'field' ? error.value : undefined
        }));
        console.log('🔍 Validation Errors:', {
            url: req.url,
            method: req.method,
            body: req.body,
            errors: formattedErrors
        });
        utils_1.ResponseUtils.validationError(res, formattedErrors, 'Validation failed');
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
const validate = (validations) => {
    return [
        ...validations,
        exports.handleValidationErrors
    ];
};
exports.validate = validate;
//# sourceMappingURL=validation.js.map