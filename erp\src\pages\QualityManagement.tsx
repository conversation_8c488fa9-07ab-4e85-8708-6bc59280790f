import React, { useState } from 'react';
import { Users, Settings } from 'lucide-react';
import QualityPersonnel from './quality/QualityPersonnel';
import QualityProcess from './quality/QualityProcess';

const QualityManagement = () => {
  const [activeTab, setActiveTab] = useState('personnel');

  const tabs = [
    {
      id: 'personnel',
      name: 'Phòng chất lượng nhân sự',
      icon: Users,
      component: QualityPersonnel
    },
    {
      id: 'process',
      name: '<PERSON>òng chất lượng quy trình',
      icon: Settings,
      component: QualityProcess
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || QualityPersonnel;

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900 mb-4"><PERSON><PERSON> phận chất l<PERSON></h1>
        <div className="flex space-x-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default QualityManagement;
