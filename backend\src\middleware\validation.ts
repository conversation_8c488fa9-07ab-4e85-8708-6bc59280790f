import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { ResponseUtils } from '@/utils';

/**
 * Middleware to handle validation results
 */
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? (error as any).path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? (error as any).value : undefined
    }));

    console.log('🔍 Validation Errors:', {
      url: req.url,
      method: req.method,
      body: req.body,
      errors: formattedErrors
    });

    ResponseUtils.validationError(res, formattedErrors, 'Validation failed');
    return;
  }

  next();
};

/**
 * Combine validation rules with error handling
 */
export const validate = (validations: Validation<PERSON>hain[]) => {
  return [
    ...validations,
    handleValidationErrors
  ];
};
