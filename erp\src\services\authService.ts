import { LoginRequest, RegisterRequest, AuthResponse, ApiResponse, User, PaginatedResponse } from '../types/auth';

const API_BASE_URL = 'http://localhost:5000/api/v1';

class AuthService {
  private static getHeaders(includeAuth = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (includeAuth) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  private static async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'An error occurred');
    }
    
    return data;
  }

  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(credentials),
      });

      const result = await this.handleResponse<AuthResponse>(response);
      
      if (result.success && result.data) {
        // Store tokens in localStorage
        localStorage.setItem('accessToken', result.data.accessToken);
        localStorage.setItem('refreshToken', result.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(result.data.user));
        
        return result.data;
      }
      
      throw new Error(result.message || 'Login failed');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      // Remove confirmPassword before sending to API
      const { confirmPassword, ...registerData } = userData;
      
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(registerData),
      });

      const result = await this.handleResponse<AuthResponse>(response);
      
      if (result.success && result.data) {
        // Store tokens in localStorage
        localStorage.setItem('accessToken', result.data.accessToken);
        localStorage.setItem('refreshToken', result.data.refreshToken);
        localStorage.setItem('user', JSON.stringify(result.data.user));
        
        return result.data;
      }
      
      throw new Error(result.message || 'Registration failed');
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  static async logout(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: this.getHeaders(true),
          body: JSON.stringify({ refreshToken }),
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  }

  static async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        return null;
      }

      const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ refreshToken }),
      });

      const result = await this.handleResponse<{ accessToken: string; refreshToken: string }>(response);
      
      if (result.success && result.data) {
        localStorage.setItem('accessToken', result.data.accessToken);
        localStorage.setItem('refreshToken', result.data.refreshToken);
        return result.data.accessToken;
      }
      
      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.logout(); // Clear invalid tokens
      return null;
    }
  }

  static getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  static getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken() && !!this.getCurrentUser();
  }

  // User management methods (Admin/Manager only)
  static async getAllUsers(page = 1, limit = 10): Promise<PaginatedResponse<User>> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/users?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: this.getHeaders(true),
      });

      const result = await this.handleResponse<PaginatedResponse<User>>(response);

      if (result.success && result.data) {
        return result.data;
      }

      throw new Error(result.message || 'Failed to get users');
    } catch (error) {
      console.error('Get all users error:', error);
      throw error;
    }
  }

  static async getUserById(id: string): Promise<User> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/users/${id}`, {
        method: 'GET',
        headers: this.getHeaders(true),
      });

      const result = await this.handleResponse<User>(response);

      if (result.success && result.data) {
        return result.data;
      }

      throw new Error(result.message || 'Failed to get user');
    } catch (error) {
      console.error('Get user by ID error:', error);
      throw error;
    }
  }

  static async searchUsers(query: string, page = 1, limit = 10): Promise<PaginatedResponse<User>> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/users/search/${encodeURIComponent(query)}?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: this.getHeaders(true),
      });

      const result = await this.handleResponse<PaginatedResponse<User>>(response);

      if (result.success && result.data) {
        return result.data;
      }

      throw new Error(result.message || 'Failed to search users');
    } catch (error) {
      console.error('Search users error:', error);
      throw error;
    }
  }

  static async getProfile(): Promise<User> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        method: 'GET',
        headers: this.getHeaders(true),
      });

      const result = await this.handleResponse<User>(response);

      if (result.success && result.data) {
        return result.data;
      }

      throw new Error(result.message || 'Failed to get profile');
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }
}

export default AuthService;
