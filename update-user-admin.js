// Script to update user role to admin using backend models
const mongoose = require('./backend/node_modules/mongoose');

async function updateUserToAdmin() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/mydatabase');
    console.log('Connected to MongoDB');

    // Define User schema (simplified)
    const userSchema = new mongoose.Schema({
      username: String,
      email: String,
      role: String
    });

    const User = mongoose.model('User', userSchema);

    // Find user <NAME_EMAIL>
    const user = await User.findOne({ email: '<EMAIL>' });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('📋 Current user:', {
      username: user.username,
      email: user.email,
      role: user.role
    });

    // Update user role to admin
    const result = await User.updateOne(
      { email: '<EMAIL>' },
      { $set: { role: 'admin' } }
    );

    if (result.modifiedCount > 0) {
      console.log('✅ User role updated to admin successfully');

      // Verify update
      const updatedUser = await User.findOne({ email: '<EMAIL>' });
      console.log('📋 Updated user:', {
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role
      });
    } else {
      console.log('❌ Failed to update user role');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

updateUserToAdmin();
