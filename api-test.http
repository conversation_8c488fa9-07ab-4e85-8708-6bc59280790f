# API Testing File for VS Code REST Client Extension
# Install "REST Client" extension in VS Code to use this file

### Variables
@baseUrl = http://localhost:5000/api/v1
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************.Zt8Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5E

### 1. Login to get fresh token
POST {{baseUrl}}/auth/login
Content-Type: application/json
Origin: http://localhost:5173

{
  "email": "<EMAIL>",
  "password": "Test123456"
}

### 2. Get all users (Admin/Manager only)
GET {{baseUrl}}/auth/users
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. Get user by ID
GET {{baseUrl}}/auth/users/6853da94f2917b370e3a6852
Authorization: Bearer {{token}}
Content-Type: application/json

### 4. Search users
GET {{baseUrl}}/auth/users/search/admin
Authorization: Bearer {{token}}
Content-Type: application/json

### 5. Get current user profile
GET {{baseUrl}}/auth/profile
Authorization: Bearer {{token}}
Content-Type: application/json

### 6. Register new user
POST {{baseUrl}}/auth/register
Content-Type: application/json
Origin: http://localhost:5173

{
  "username": "testuser456",
  "email": "<EMAIL>",
  "password": "Test123456",
  "firstName": "Test",
  "lastName": "User456",
  "role": "employee"
}

### 7. Test without token (should fail)
GET {{baseUrl}}/auth/users
Content-Type: application/json

### 8. Test with employee token (should fail for user management)
# First login as employee, then use that token
POST {{baseUrl}}/auth/login
Content-Type: application/json
Origin: http://localhost:5173

{
  "email": "<EMAIL>",
  "password": "Employee123456"
}
