#!/usr/bin/env python3
"""
API Testing Script for ERP System
Run with: python test-api.py
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:5000/api/v1"

def print_response(title, response):
    """Print formatted response"""
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print(f"{'='*50}")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

def test_api():
    """Test all API endpoints"""
    print("🚀 Starting ERP API Tests...")
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Test Login
    print("\n1️⃣ Testing Login...")
    login_data = {
        "email": "<EMAIL>",
        "password": "Test123456"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:5173"
    }
    
    try:
        login_response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers=headers
        )
        
        print_response("Login", login_response)
        
        if login_response.status_code == 200:
            token = login_response.json()["data"]["accessToken"]
            print(f"✅ Login successful! Token: {token[:50]}...")
            
            # Update headers with token
            auth_headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 2. Test Get All Users
            print("\n2️⃣ Testing Get All Users...")
            users_response = requests.get(
                f"{BASE_URL}/auth/users",
                headers=auth_headers
            )
            print_response("Get All Users", users_response)
            
            # 3. Test Get Profile
            print("\n3️⃣ Testing Get Profile...")
            profile_response = requests.get(
                f"{BASE_URL}/auth/profile",
                headers=auth_headers
            )
            print_response("Get Profile", profile_response)
            
            # 4. Test Search Users
            print("\n4️⃣ Testing Search Users...")
            search_response = requests.get(
                f"{BASE_URL}/auth/users/search/admin",
                headers=auth_headers
            )
            print_response("Search Users", search_response)
            
            # 5. Test Get User by ID (if users exist)
            if users_response.status_code == 200:
                users_data = users_response.json()
                if users_data["data"]["data"]:
                    user_id = users_data["data"]["data"][0]["_id"]
                    print(f"\n5️⃣ Testing Get User by ID: {user_id}")
                    user_response = requests.get(
                        f"{BASE_URL}/auth/users/{user_id}",
                        headers=auth_headers
                    )
                    print_response("Get User by ID", user_response)
            
            # 6. Test without token (should fail)
            print("\n6️⃣ Testing without token (should fail)...")
            no_token_response = requests.get(
                f"{BASE_URL}/auth/users",
                headers={"Content-Type": "application/json"}
            )
            print_response("No Token Test", no_token_response)
            
        else:
            print("❌ Login failed!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure backend is running on localhost:5000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print(f"\n🎉 API Testing completed at {datetime.now().strftime('%H:%M:%S')}")

if __name__ == "__main__":
    test_api()
