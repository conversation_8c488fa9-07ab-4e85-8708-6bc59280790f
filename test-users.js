// Test script for user endpoints
const API_BASE = 'http://localhost:5000/api/v1';

async function testUserEndpoints() {
  try {
    console.log('🔍 Testing User Endpoints...\n');

    // 1. Login as existing admin user
    console.log('1. Logging in as admin...');
    const adminResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123456'
      })
    });

    const adminData = await adminResponse.json();
    console.log('Admin login:', adminData.success ? '✅' : '❌');

    if (!adminData.success) {
      console.log('Admin login failed:', adminData.message);
      return;
    }

    const adminToken = adminData.data.accessToken;
    console.log('Admin token:', adminToken.substring(0, 50) + '...\n');

    // 2. Create regular user
    console.log('2. Creating regular user...');
    const userResponse = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173'
      },
      body: JSON.stringify({
        username: 'employee1',
        email: '<EMAIL>',
        password: 'Employee123456',
        firstName: 'Employee',
        lastName: 'One'
      })
    });

    const userData = await userResponse.json();
    console.log('Employee created:', userData.success ? '✅' : '❌\n');

    // 3. Test get all users (admin only)
    console.log('3. Testing GET /auth/users (admin only)...');
    const usersResponse = await fetch(`${API_BASE}/auth/users`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    const usersData = await usersResponse.json();
    console.log('Get all users:', usersData.success ? '✅' : '❌');
    if (usersData.success) {
      console.log('Total users:', usersData.data.data.length);
      console.log('Pagination:', usersData.data.pagination);
    } else {
      console.log('Error:', usersData.message);
    }
    console.log('');

    // 4. Test get user by ID
    if (usersData.success && usersData.data.data.length > 0) {
      const firstUserId = usersData.data.data[0]._id;
      console.log('4. Testing GET /auth/users/:id...');
      
      const userByIdResponse = await fetch(`${API_BASE}/auth/users/${firstUserId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      const userByIdData = await userByIdResponse.json();
      console.log('Get user by ID:', userByIdData.success ? '✅' : '❌');
      if (userByIdData.success) {
        console.log('User:', userByIdData.data.username, '-', userByIdData.data.email);
      }
      console.log('');
    }

    // 5. Test search users
    console.log('5. Testing GET /auth/users/search/:query...');
    const searchResponse = await fetch(`${API_BASE}/auth/users/search/admin`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    const searchData = await searchResponse.json();
    console.log('Search users:', searchData.success ? '✅' : '❌');
    if (searchData.success) {
      console.log('Search results:', searchData.data.data.length);
    }
    console.log('');

    // 6. Test get profile
    console.log('6. Testing GET /auth/profile...');
    const profileResponse = await fetch(`${API_BASE}/auth/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    const profileData = await profileResponse.json();
    console.log('Get profile:', profileData.success ? '✅' : '❌');
    if (profileData.success) {
      console.log('Profile:', profileData.data.username, '-', profileData.data.role);
    }

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run tests
testUserEndpoints();
